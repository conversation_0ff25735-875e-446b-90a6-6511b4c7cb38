#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验界面显示模块
使用PsychoPy创建各种实验界面
"""

import time
from typing import Optional, Tuple, List

try:
    from psychopy import visual, core, event, gui, monitors
    from psychopy.visual import TextBox2
    from psychopy.constants import STARTED, FINISHED
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

class ExperimentDisplay:
    """实验显示管理类"""
    
    def __init__(self, fullscreen: bool = True, screen_size: Tuple[int, int] = (1920, 1080), ):
        """
        初始化显示窗口
        
        Args:
            fullscreen: 是否全屏显示
            screen_size: 屏幕尺寸
        """
        self.fullscreen = fullscreen
        self.screen_size = screen_size
        self.win = None
        self.dummy_mode = not PSYCHOPY_AVAILABLE
        
        if not self.dummy_mode:
            self._create_window()
        else:
            print("PsychoPy不可用，使用模拟模式")
    
    def _create_window(self):
        """创建PsychoPy窗口"""
        try:
            # self.win = visual.Window(
            #     # size=self.screen_size,
            #     fullscr=self.fullscreen,
            #     screen=0,
            #     allowGUI=True,
            #     allowStencil=False,
            #     monitor='testMonitor',
            #     color=[0, 0, 0],  # 黑色背景
            #     colorSpace='rgb',
            #     blendMode='avg',
            #     useFBO=True,
            #     units='pix'
            # )
            # print("PsychoPy窗口创建成功")
            # 创建全屏窗口
            # mon = monitors.Monitor('myMonitor', width=53.0, distance=70.0)
            # self.win = visual.Window(
            #     fullscr=True,
            #     monitor=mon,
            #     winType='pyglet',
            #     units='pix',
            #     color='gray'
            # )
            
            # # 获取屏幕分辨率
            # scn_width, scn_height = self.win.size
            # print(f"屏幕分辨率: {scn_width} x {scn_height}")
            
            # # 发送屏幕坐标给EyeLink
            # if self.el_tracker:
            #     el_coords = f"screen_pixel_coords = 0 0 {scn_width - 1} {scn_height - 1}"
            #     self.el_tracker.sendCommand(el_coords)
                
            #     # 发送DISPLAY_COORDS消息给Data Viewer
            #     dv_coords = f"DISPLAY_COORDS 0 0 {scn_width - 1} {scn_height - 1}"
            #     self.el_tracker.sendMessage(dv_coords)
            
            # Set the screen resolution
            (scn_w, scn_h) = self.screen_size

            # Open a window, be sure to specify the monitor resolution
            mon = monitors.Monitor('projMonitor', width=53.0, distance=70.0)
            mon.setSizePix((scn_w, scn_h))
            self.win = visual.Window((scn_w, scn_h),
                                fullscr=True,
                                screen=1,
                                monitor=mon,
                                winType='pyglet',
                                units='pix')
            
            print("✓ 显示窗口设置成功")
        except Exception as e:
            print(f"创建PsychoPy窗口失败: {e}")
            self.dummy_mode = True
    
    def show_fixation_cross(self, duration: float = 5.0, eyelink_manager=None, perform_drift_correction: bool = False) -> bool:
        """
        显示十字准星，可选择进行眼动校准

        Args:
            duration: 显示时长（秒）
            eyelink_manager: EyeLink管理器对象
            perform_drift_correction: 是否进行漂移校正

        Returns:
            是否正常完成显示
        """
        if self.dummy_mode:
            print(f"模拟模式：显示十字准星 {duration} 秒")
            if perform_drift_correction:
                print("模拟模式：执行漂移校正")
            time.sleep(min(duration, 2))  # 模拟时缩短时间
            return True

        try:
            # 获取窗口尺寸
            win_width, win_height = self.win.size
            center_x, center_y = 0, 0  # PsychoPy坐标系中心点

            # 创建十字准星
            fixation = visual.ShapeStim(
                self.win,
                vertices=((0, -20), (0, 20), (0, 0), (-20, 0), (20, 0)),
                lineWidth=3,
                closeShape=False,
                lineColor='white'
            )

            # 如果需要进行漂移校正
            if perform_drift_correction and eyelink_manager:
                print("开始漂移校正...")

                # 显示十字准星并提示被试注视
                instruction_text = visual.TextStim(
                    self.win,
                    text="请注视中央十字准星\n看准后按enter键继续",
                    font='SimHei',
                    pos=(0, -100),
                    height=24,
                    color='white'  # 使用白色，不刺激
                )
                
                
                # 显示指示文本1秒
                for _ in range(120):  # 2秒 * 60fps
                    fixation.draw()
                    instruction_text.draw()
                    self.win.flip()
                    time.sleep(0.016)

                # 执行漂移校正
                try:
                    # 转换坐标：PsychoPy坐标系转换为像素坐标系
                    # PsychoPy: 中心(0,0), 像素坐标系: 左上角(0,0)
                    pixel_x = int(win_width // 2 + center_x)
                    pixel_y = int(win_height // 2 - center_y)

                    print(f"执行漂移校正，目标位置: PsychoPy({center_x}, {center_y}) -> 像素({pixel_x}, {pixel_y})")

                    # 调用EyeLink的漂移校正
                    # doDriftCorrect(x, y, draw_target, allow_setup)
                    # x, y: 目标位置（像素坐标）
                    # draw_target: 1=自动绘制目标, 0=手动绘制
                    # allow_setup: 1=允许按ESC进入设置, 0=不允许
                    error = eyelink_manager.tracker.doDriftCorrect(pixel_x, pixel_y, 0, 1)

                    if error == 0:
                        print("✓ 漂移校正成功")
                        # 显示成功提示
                        success_text = visual.TextStim(
                            self.win,
                            text="校正成功！",
                            font='SimHei',
                            pos=(0, -100),
                            height=24,
                            color='green'
                        )
                        for _ in range(60):  # 1秒
                            fixation.draw()
                            success_text.draw()
                            self.win.flip()
                            time.sleep(0.016)
                    elif error == 27:  # ESC键
                        print("用户按ESC键，进入校准设置")
                        return False
                    else:
                        print(f"漂移校正失败，错误代码: {error}")

                except Exception as e:
                    print(f"漂移校正执行失败: {e}")
            else:
                # 否则正常显示十字准星
                start_time = time.time()
                while time.time() - start_time < duration:
                    fixation.draw()
                    self.win.flip()

                    # 检查是否有按键退出
                    keys = event.getKeys()
                    if 'escape' in keys:
                        return False

            return True

        except Exception as e:
            print(f"显示十字准星失败: {e}")
            return False
    
    def show_question(self, question: str, duration: float = None) -> bool:
        """
        显示问题 - 自动多行显示，按回车继续

        Args:
            question: 问题文本
            duration: 保留参数（兼容性），实际按回车继续

        Returns:
            是否正常完成显示
        """
        if self.dummy_mode:
            print(f"模拟模式：显示问题 {duration} 秒")
            print(f"问题：{question[:50]}...")
            time.sleep(min(duration, 2))
            return True

        try:
            # 获取窗口尺寸
            win_width, win_height = self.win.size

            # 根据屏幕尺寸计算合适的文本参数
            margin = 100  # 左右边距
            effective_width = win_width - 2 * margin

            # 根据屏幕宽度调整字体大小和换行宽度
            if win_width >= 1920:
                # 1920*1080或更高分辨率
                font_height = 36
                wrap_width = effective_width * 0.9
            elif win_width >= 1600:
                # 1600*900等中等分辨率
                font_height = 32
                wrap_width = effective_width * 0.85
            else:
                # 较小分辨率
                font_height = 28
                wrap_width = effective_width * 0.8

            # 创建问题文本 - 使用自动换行
            # question_text = visual.TextStim(
            #     self.win,
            #     text=question,
            #     font='SimHei',  # 中文字体
            #     pos=(0, 0),
            #     height=font_height,
            #     color='white',
            #     wrapWidth=wrap_width,
            #     alignText='center'  # 居中对齐
            # )
            question_text = visual.TextStim(
                self.win,
                text=question,
                font='SimHei',
                pos=(0, 0),                     # 以屏幕中心为参考点
                height=font_height,
                color='white',
                wrapWidth=wrap_width,           # ← 用上面刚算好的宽度
                anchorHoriz='center',           # [新增] PsychoPy ≥ 2021
                anchorVert='center'             # [新增]
                # 旧版可替换为 alignHoriz='center', alignVert='center'
            )

            # 添加提示文本
            hint_text = visual.TextStim(
                self.win,
                text="按回车键继续，按 Esc 键跳过",
                font='SimHei',
                pos=(0, -win_height//2 + 50),
                height=20,
                color='gray'
            )

            print(f"问题显示参数: 窗口尺寸={win_width}x{win_height}, 字体大小={font_height}, 换行宽度={wrap_width:.0f}")

            # 显示问题，等待回车键
            while True:
                question_text.draw()
                hint_text.draw()
                self.win.flip()

                # 检查按键
                keys = event.getKeys()
                if 'escape' in keys:
                    print("用户按Esc键跳过问题显示")
                    return False
                elif 'return' in keys:
                    print("用户按回车键继续")
                    return True

        except Exception as e:
            print(f"显示问题失败: {e}")
            return False
    
    def get_text_input(self, prompt: str = "请输入您的答案：", wait_for_return: bool = True) -> Optional[str]:
        """
        获取文本输入 - 支持中文输入，按回车确认

        Args:
            prompt: 提示文本
            wait_for_return: 是否等待回车键确认

        Returns:
            用户输入的文本，如果取消则返回None
        """
        if self.dummy_mode:
            print(f"模拟模式：获取文本输入")
            print(f"提示：{prompt}")
            time.sleep(1)
            return "模拟答案"

        try:
            # 尝试使用TextBox2（新版本PsychoPy）
            return self._get_text_input_textbox2(prompt)
        except Exception as e:
            print(f"TextBox2输入失败: {e}")
            # 回退到改进的事件输入方法
            return self._get_text_input_unicode(prompt)

    def _get_text_input_textbox2(self, prompt: str) -> Optional[str]:
        """
        使用TextBox2的中文输入方法
        """
        # 创建提示文本
        prompt_text = visual.TextStim(
            self.win,
            text=prompt,
            font='SimHei',
            pos=(0, 150),
            height=30,
            color='white'
        )

        # 创建说明文本
        # instruction_text = visual.TextStim(
        #     self.win,
        #     text="输入完成后按回车键确认，按ESC键取消",
        #     font='SimHei',
        #     pos=(0, -100),
        #     height=20,
        #     color='yellow'
        # )

        # 使用TextBox2支持中文输入
        textbox = TextBox2(
            self.win,
            text='',
            font='SimHei',
            pos=(0, 0),
            size=(800, 60),
            letterHeight=25,
            color='black',
            fillColor='white',
            borderColor='gray',
            borderWidth=2,
            editable=True
        )

        # 设置焦点到文本框
        textbox.hasFocus = True

        while True:
            # 绘制界面
            prompt_text.draw()
            # instruction_text.draw()
            textbox.draw()
            self.win.flip()

            # 处理按键
            keys = event.getKeys()
            for key in keys:
                if key == 'escape':
                    return None
                elif key == 'return':
                    user_input = textbox.text.strip()
                    return user_input if user_input else None

            # 检查TextBox2是否有更新方法
            if hasattr(textbox, 'refresh'):
                textbox.refresh()
            elif hasattr(textbox, 'update'):
                textbox.update()
            # 如果没有更新方法，让PsychoPy自然处理

    def _get_text_input_unicode(self, prompt: str) -> Optional[str]:
        """
        使用Unicode事件处理的中文输入方法
        """
        # 创建提示文本
        prompt_text = visual.TextStim(
            self.win,
            text=prompt,
            font='SimHei',
            pos=(0, 150),
            height=30,
            color='white'
        )

        # 创建说明文本
        # instruction_text = visual.TextStim(
        #     self.win,
        #     text="输入完成后按回车键确认，按ESC键取消",
        #     font='SimHei',
        #     pos=(0, -100),
        #     height=20,
        #     color='yellow'
        # )

        # 创建输入框背景
        input_box = visual.Rect(
            self.win,
            width=800,
            height=60,
            pos=(0, 0),
            fillColor='white',
            lineColor='gray',
            lineWidth=2
        )

        # 输入文本显示
        input_text = visual.TextStim(
            self.win,
            text='',
            font='SimHei',
            pos=(0, 0),
            height=25,
            color='black'
        )

        user_input = ""

        # 清除事件缓冲区
        event.clearEvents()

        while True:
            # 绘制界面
            prompt_text.draw()
            # instruction_text.draw()
            input_box.draw()
            input_text.setText(user_input + "|")  # 添加光标
            input_text.draw()
            self.win.flip()

            # 获取所有事件，包括Unicode字符
            keys = event.getKeys(keyList=None, timeStamped=False)

            for key in keys:
                if key == 'escape':
                    return None
                elif key == 'return':
                    return user_input.strip() if user_input.strip() else None
                elif key == 'backspace':
                    user_input = user_input[:-1]
                elif key == 'space':
                    user_input += ' '
                elif len(key) == 1:
                    # 单个字符，包括中文字符
                    user_input += key
                elif key.startswith('num_'):
                    # 数字键盘
                    num = key.replace('num_', '')
                    if num.isdigit():
                        user_input += num

            # 限制输入长度
            if len(user_input) > 200:
                user_input = user_input[:200]

    def _get_text_input_fallback(self, prompt: str) -> Optional[str]:
        """
        备用文本输入方法（当TextBox2不可用时）
        """
        try:
            # 创建提示文本
            prompt_text = visual.TextStim(
                self.win,
                text=prompt,
                font='SimHei',
                pos=(0, 100),
                height=30,
                color='white'
            )

            # 创建说明文本
            instruction_text = visual.TextStim(
                self.win,
                text="输入完成后按回车键确认，按ESC键取消",
                font='SimHei',
                pos=(0, -100),
                height=20,
                color='yellow'
            )

            # 创建输入框背景
            input_box = visual.Rect(
                self.win,
                width=800,
                height=60,
                pos=(0, 0),
                fillColor='white',
                lineColor='gray'
            )

            # 输入文本显示
            input_text = visual.TextStim(
                self.win,
                text='',
                font='SimHei',
                pos=(0, 0),
                height=25,
                color='black'
            )

            user_input = ""

            while True:
                # 绘制界面
                prompt_text.draw()
                instruction_text.draw()
                input_box.draw()
                input_text.setText(user_input + "|")  # 添加光标
                input_text.draw()
                self.win.flip()

                # 处理按键
                keys = event.getKeys()
                for key in keys:
                    if key == 'escape':
                        return None
                    elif key == 'return':
                        return user_input if user_input else None
                    elif key == 'backspace':
                        user_input = user_input[:-1]
                    elif len(key) == 1 and len(user_input) < 100:
                        user_input += key

        except Exception as e:
            print(f"备用文本输入也失败: {e}")
            return None
    
    def get_rating(self, question: str, scale_range: Tuple[int, int] = (1, 5),
                   labels: List[str] = None, duration: float = None) -> Optional[int]:
        """
        获取评分 - 不限时，按回车确认

        Args:
            question: 评分问题
            scale_range: 评分范围
            labels: 评分标签
            duration: 保留参数（兼容性），实际不限时

        Returns:
            用户选择的评分，如果取消则返回None
        """
        if self.dummy_mode:
            print(f"模拟模式：获取评分")
            print(f"问题：{question}")
            time.sleep(1)
            return 3  # 模拟中等评分
        
        if labels is None:
            labels = [str(i) for i in range(scale_range[0], scale_range[1] + 1)]
        
        try:
            # 创建问题文本
            question_text = visual.TextStim(
                self.win,
                text=question,
                font='SimHei',
                pos=(0, 200),
                height=30,
                color='white',
                wrapWidth=1600
            )
            
            # 创建评分选项
            rating_options = []
            num_options = scale_range[1] - scale_range[0] + 1
            spacing = 800 / (num_options - 1) if num_options > 1 else 0
            start_x = -400 if num_options > 1 else 0
            
            for i, (value, label) in enumerate(zip(range(scale_range[0], scale_range[1] + 1), labels)):
                x_pos = start_x + i * spacing
                
                # 数字
                number = visual.TextStim(
                    self.win,
                    text=str(value),
                    font='SimHei',
                    pos=(x_pos, 0),
                    height=40,
                    color='white'
                )
                
                # 标签
                label_text = visual.TextStim(
                    self.win,
                    text=label,
                    font='SimHei',
                    pos=(x_pos, -50),
                    height=20,
                    color='lightgray',
                    wrapWidth=150
                )
                
                rating_options.append((value, number, label_text))
            
            # 提示文本
            # 创建指示文本
            instruction = visual.TextStim(
                self.win,
                text="请按数字键选择评分，然后按回车确认",
                font='SimHei',
                pos=(0, -150),
                height=25,
                color='yellow'
            )

            selected_rating = None

            while True:
                # 绘制界面
                question_text.draw()
                instruction.draw()

                for value, number, label_text in rating_options:
                    # 如果是选中的评分，高亮显示
                    if selected_rating == value:
                        number.color = 'yellow'
                        label_text.color = 'yellow'
                    else:
                        number.color = 'white'
                        label_text.color = 'lightgray'

                    number.draw()
                    label_text.draw()

                self.win.flip()

                # 处理按键
                keys = event.getKeys()
                for key in keys:
                    if key == 'escape':
                        return None
                    elif key == 'return' and selected_rating is not None:
                        return selected_rating
                    elif key.isdigit():
                        rating = int(key)
                        if scale_range[0] <= rating <= scale_range[1]:
                            selected_rating = rating
            
        except Exception as e:
            print(f"获取评分失败: {e}")
            return None
    
    def show_answer(self, answer: str, eyelink_manager=None, gaze_duration_threshold: float = 1.0) -> bool:
        """
        显示答案 - 自动多行显示，动态时间控制
        在屏幕中下方显示"读完看我哦"按钮，当眼动持续注视该区域指定时间后自动切换

        Args:
            answer: 答案文本
            eyelink_manager: EyeLink管理器对象，用于获取眼动数据
            gaze_duration_threshold: 注视按钮区域的持续时间阈值（秒）

        Returns:
            是否正常完成显示
        """
        if self.dummy_mode:
            print(f"模拟模式：显示答案（动态时间控制）")
            print(f"答案：{answer[:50]}...")
            time.sleep(2)  # 模拟模式下固定2秒
            return True

        try:
            # 获取窗口尺寸
            win_width, win_height = self.win.size

            # 根据屏幕尺寸计算合适的文本参数
            margin = 100  # 左右边距
            effective_width = win_width - 2 * margin

            # 根据屏幕宽度调整字体大小和换行宽度
            if win_width >= 1920:
                font_height = 32
                wrap_width = effective_width * 0.9
            elif win_width >= 1600:
                font_height = 28
                wrap_width = effective_width * 0.85
            else:
                font_height = 24
                wrap_width = effective_width * 0.8

            # 创建答案标题
            title_text = visual.TextStim(
                self.win,
                text="答案：",
                font='SimHei',
                pos=(0, win_height//2 - 80),
                height=font_height + 8,
                color='yellow',
                bold=True
            )

            # 创建答案文本
            answer_text = visual.TextStim(
                self.win,
                text=answer,
                font='SimHei',
                pos=(0, 50),  # 稍微上移，为按钮留出空间
                height=font_height,
                color='lightgreen',
                wrapWidth=wrap_width,
                anchorHoriz='center',
                anchorVert='center'
            )

            # 创建"读完看我哦"按钮区域
            button_width = 300
            button_height = 150
            button_y = -win_height//2 + 100  # 距离底部100像素

            # 按钮背景矩形
            button_rect = visual.Rect(
                self.win,
                width=button_width,
                height=button_height,
                pos=(0, button_y),
                fillColor='gray',
                lineColor='white',
                lineWidth=2
            )

            # 按钮文字
            button_text = visual.TextStim(
                self.win,
                text="读完看我哦",
                font='SimHei',
                pos=(0, button_y),
                height=24,
                color='white',
                bold=True
            )

            # 添加提示文本
            hint_text = visual.TextStim(
                self.win,
                text="按回车键继续，或按 Esc 键跳过",
                font='SimHei',
                pos=(0, -win_height//2 + 50),
                height=18,
                color='gray'
            )

            print(f"答案显示参数: 窗口尺寸={win_width}x{win_height}, 字体大小={font_height}")
            print(f"按钮区域: 中心({0}, {button_y}), 尺寸{button_width}x{button_height}")

            # 眼动注视检测变量
            gaze_start_time = None
            button_bounds = {
                'left': -button_width // 2,
                'right': button_width // 2,
                'top': button_y + button_height // 2,
                'bottom': button_y - button_height // 2
            }

            # 显示答案并检测眼动
            start_time = time.time()
            while True:
                # 绘制所有元素
                title_text.draw()
                answer_text.draw()
                # button_rect.draw()
                # button_text.draw()
                # hint_text.draw()
                self.win.flip()

                # 检查按键
                keys = event.getKeys()
                if 'escape' in keys:
                    print("用户按Esc键跳过答案显示")
                    return False
                elif 'return' in keys:
                    print("用户按回车键继续")
                    return True

                # 暂时注释掉眼动检测功能
                # # 检查眼动数据（如果有EyeLink）
                # if eyelink_manager and hasattr(eyelink_manager, 'get_current_gaze_position'):
                #     try:
                #         gaze_pos = eyelink_manager.get_current_gaze_position()
                #         if gaze_pos:
                #             gaze_x, gaze_y = gaze_pos
                #
                #             # 检查是否在按钮区域内
                #             if (button_bounds['left'] <= gaze_x <= button_bounds['right'] and
                #                 button_bounds['bottom'] <= gaze_y <= button_bounds['top']):
                #
                #                 if gaze_start_time is None:
                #                     gaze_start_time = time.time()
                #                     print(f"开始注视按钮区域: ({gaze_x:.1f}, {gaze_y:.1f})")
                #                 elif time.time() - gaze_start_time >= gaze_duration_threshold:
                #                     print(f"注视按钮区域{gaze_duration_threshold}秒，自动继续")
                #                     return True
                #             else:
                #                 gaze_start_time = None
                #     except Exception as e:
                #         print(f"获取眼动数据失败: {e}")

                time.sleep(0.016)  # 约60fps刷新率

        except Exception as e:
            print(f"显示答案失败: {e}")
            return False
    
    def close(self):
        """关闭显示窗口"""
        if self.win:
            self.win.close()
            print("显示窗口已关闭")

def test_display():
    """测试显示功能"""
    print("测试显示功能...")
    
    # 创建显示管理器
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 测试十字准星
        print("测试十字准星...")
        display.show_fixation_cross(2)
        
        # 测试问题显示
        print("测试问题显示...")
        display.show_question("这是一个测试问题？", 3)
        
        # 测试文本输入
        print("测试文本输入...")
        answer = display.get_text_input("请输入测试答案：", 5)
        print(f"用户输入：{answer}")
        
        # 测试评分
        print("测试评分...")
        rating = display.get_rating("您觉得这个测试如何？", (1, 5), 
                                   ["很差", "较差", "一般", "较好", "很好"], 5)
        print(f"用户评分：{rating}")
        
        # 测试答案显示
        print("测试答案显示...")
        display.show_answer("这是测试答案的内容。", 3)
        
    finally:
        display.close()
    
    print("显示功能测试完成")

if __name__ == "__main__":
    test_display()
