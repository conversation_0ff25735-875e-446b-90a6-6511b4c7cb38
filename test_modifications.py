#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修改后的功能 - 简化版本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from experiment_display import ExperimentDisplay
        print("✓ experiment_display 导入成功")
        
        from eyelink_manager import EyeLinkManager
        print("✓ eyelink_manager 导入成功")
        
        from experiment_flow import CuriosityExperiment
        print("✓ experiment_flow 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_display_methods():
    """测试显示方法的新参数"""
    print("\n测试显示方法...")
    
    try:
        from experiment_display import ExperimentDisplay
        
        # 创建显示对象（虚拟模式）
        display = ExperimentDisplay(fullscreen=False)
        
        # 检查show_answer方法的新参数
        import inspect
        answer_sig = inspect.signature(display.show_answer)
        answer_params = list(answer_sig.parameters.keys())
        print(f"show_answer 参数: {answer_params}")
        
        expected_params = ['answer', 'eyelink_manager', 'gaze_duration_threshold']
        if all(param in answer_params for param in expected_params):
            print("✓ show_answer 方法参数正确")
        else:
            print("✗ show_answer 方法参数不完整")
        
        # 检查show_fixation_cross方法的新参数
        fixation_sig = inspect.signature(display.show_fixation_cross)
        fixation_params = list(fixation_sig.parameters.keys())
        print(f"show_fixation_cross 参数: {fixation_params}")
        
        expected_fixation_params = ['duration', 'eyelink_manager', 'perform_drift_correction']
        if all(param in fixation_params for param in expected_fixation_params):
            print("✓ show_fixation_cross 方法参数正确")
        else:
            print("✗ show_fixation_cross 方法参数不完整")
        
        display.close()
        return True
        
    except Exception as e:
        print(f"✗ 显示方法测试失败: {e}")
        return False

def test_eyelink_methods():
    """测试EyeLink新方法"""
    print("\n测试EyeLink新方法...")
    
    try:
        from eyelink_manager import EyeLinkManager
        
        # 创建EyeLink对象（虚拟模式）
        eyelink = EyeLinkManager("test", "test_data", (1920, 1080), dummy_mode=True)
        
        # 检查是否有新的get_current_gaze_position方法
        if hasattr(eyelink, 'get_current_gaze_position'):
            print("✓ get_current_gaze_position 方法存在")
            
            # 测试方法调用
            gaze_pos = eyelink.get_current_gaze_position()
            print(f"眼动位置获取结果: {gaze_pos} (虚拟模式下应为None)")
        else:
            print("✗ get_current_gaze_position 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ EyeLink方法测试失败: {e}")
        return False

def test_experiment_flow():
    """测试实验流程的新参数"""
    print("\n测试实验流程...")
    
    try:
        from experiment_flow import CuriosityExperiment
        
        # 创建实验对象
        experiment = CuriosityExperiment("test", use_eyelink=False, fullscreen=False)
        
        # 检查timing参数
        timing = experiment.timing
        print(f"实验时间参数: {list(timing.keys())}")
        
        # 检查新参数
        if 'gaze_duration_threshold' in timing:
            print(f"✓ gaze_duration_threshold 参数存在: {timing['gaze_duration_threshold']}")
        else:
            print("✗ gaze_duration_threshold 参数不存在")
        
        if timing.get('answer_display') is None:
            print("✓ answer_display 设置为动态控制 (None)")
        else:
            print(f"⚠ answer_display 仍为固定时间: {timing['answer_display']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 实验流程测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("测试修改后的功能")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("显示方法", test_display_methods),
        ("EyeLink方法", test_eyelink_methods),
        ("实验流程", test_experiment_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修改成功！")
    else:
        print("⚠ 部分测试失败，请检查修改")

if __name__ == "__main__":
    main()
