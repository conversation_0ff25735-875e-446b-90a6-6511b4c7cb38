#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证回车键修改是否正确
"""

import sys
import os
import inspect

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_display_methods():
    """验证显示方法的修改"""
    print("="*50)
    print("验证显示方法修改")
    print("="*50)
    
    try:
        from experiment_display import ExperimentDisplay
        
        # 检查show_question方法
        question_sig = inspect.signature(ExperimentDisplay.show_question)
        question_params = list(question_sig.parameters.keys())
        print(f"show_question 参数: {question_params}")
        
        # 检查默认值
        duration_param = question_sig.parameters.get('duration')
        if duration_param and duration_param.default is None:
            print("✓ show_question duration参数默认为None（按回车继续）")
        else:
            print(f"⚠ show_question duration参数: {duration_param.default}")
        
        # 检查show_answer方法
        answer_sig = inspect.signature(ExperimentDisplay.show_answer)
        answer_params = list(answer_sig.parameters.keys())
        print(f"show_answer 参数: {answer_params}")
        
        # 检查get_rating方法
        rating_sig = inspect.signature(ExperimentDisplay.get_rating)
        rating_params = list(rating_sig.parameters.keys())
        print(f"get_rating 参数: {rating_params}")
        
        # 检查默认值
        rating_duration_param = rating_sig.parameters.get('duration')
        if rating_duration_param and rating_duration_param.default is None:
            print("✓ get_rating duration参数默认为None（不限时）")
        else:
            print(f"⚠ get_rating duration参数: {rating_duration_param.default}")
        
        return True
        
    except Exception as e:
        print(f"✗ 显示方法验证失败: {e}")
        return False

def verify_experiment_flow():
    """验证实验流程的修改"""
    print("\n" + "="*50)
    print("验证实验流程修改")
    print("="*50)
    
    try:
        from experiment_flow import CuriosityExperiment
        
        # 创建实验对象
        experiment = CuriosityExperiment("test", use_eyelink=False, fullscreen=False)
        
        # 检查timing参数
        timing = experiment.timing
        print("实验时间参数配置：")
        
        # 检查关键参数
        key_params = ['question_display', 'answer_display', 'gaze_duration_threshold']
        for param in key_params:
            if param in timing:
                value = timing[param]
                if value is None:
                    print(f"  ✓ {param}: 动态控制")
                else:
                    print(f"  • {param}: {value}")
            else:
                print(f"  ✗ {param}: 参数缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ 实验流程验证失败: {e}")
        return False

def verify_code_structure():
    """验证代码结构"""
    print("\n" + "="*50)
    print("验证代码结构")
    print("="*50)
    
    try:
        # 检查experiment_display.py中的关键字符串
        with open('experiment_display.py', 'r', encoding='utf-8') as f:
            display_content = f.read()
        
        # 检查关键修改
        checks = [
            ("按回车键继续", "问题显示提示文字"),
            ("按回车确认", "评分提示文字"),
            ("'return' in keys", "回车键检测"),
            ("color='white'", "十字准星提示文字颜色"),
            ("# 暂时注释掉眼动检测", "眼动检测注释"),
        ]
        
        for check_str, description in checks:
            if check_str in display_content:
                print(f"✓ {description}: 找到 '{check_str}'")
            else:
                print(f"✗ {description}: 未找到 '{check_str}'")
        
        # 检查experiment_flow.py中的关键修改
        with open('experiment_flow.py', 'r', encoding='utf-8') as f:
            flow_content = f.read()
        
        flow_checks = [
            ("FIXATION_DRIFT_CORRECTION", "合并的基线校准标记"),
            ("已在步骤1中完成", "瞳孔基线合并说明"),
        ]
        
        for check_str, description in flow_checks:
            if check_str in flow_content:
                print(f"✓ {description}: 找到 '{check_str}'")
            else:
                print(f"✗ {description}: 未找到 '{check_str}'")
        
        return True
        
    except Exception as e:
        print(f"✗ 代码结构验证失败: {e}")
        return False

def verify_imports():
    """验证模块导入"""
    print("\n" + "="*50)
    print("验证模块导入")
    print("="*50)
    
    try:
        from experiment_display import ExperimentDisplay
        print("✓ experiment_display 导入成功")
        
        from experiment_flow import CuriosityExperiment
        print("✓ experiment_flow 导入成功")
        
        from eyelink_manager import EyeLinkManager
        print("✓ eyelink_manager 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("回车键修改验证程序")
    print("="*80)
    print("验证以下修改：")
    print("1. 问题显示改为按回车继续")
    print("2. 答案显示改为按回车继续（眼动检测已注释）")
    print("3. 评分功能改为不限时，按回车确认")
    print("4. 十字准星合并基线校准和漂移校正")
    print("5. 提示文字使用白色")
    print("="*80)
    
    tests = [
        ("模块导入", verify_imports),
        ("显示方法", verify_display_methods),
        ("实验流程", verify_experiment_flow),
        ("代码结构", verify_code_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 验证出错: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*80)
    print("验证结果总结")
    print("="*80)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("🎉 所有验证通过！修改成功！")
        print("\n修改总结：")
        print("✓ 所有界面改为按回车键继续")
        print("✓ 评分功能不限时")
        print("✓ 眼动检测功能已暂时注释")
        print("✓ 十字准星合并基线校准和漂移校正")
        print("✓ 提示文字使用白色，不刺激")
    else:
        print("⚠ 部分验证失败，请检查修改")

if __name__ == "__main__":
    main()
