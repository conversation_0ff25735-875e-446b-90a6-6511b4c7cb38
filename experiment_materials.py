#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验材料准备模块
从实验材料库中提取题目和答案，创建随机选择机制
"""

import random
import json
import os
from typing import List, Dict, Tuple

class ExperimentMaterials:
    """实验材料管理类"""
    
    def __init__(self, materials_file="实验材料库.txt"):
        """
        初始化实验材料
        
        Args:
            materials_file: 材料库文件路径
        """
        self.materials_file = materials_file
        self.questions = []
        self.answers = []
        self.question_answer_pairs = []
        self.load_materials()
    
    def load_materials(self):
        """从文件加载实验材料"""
        try:
            with open(self.materials_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析题目和答案
            self._parse_materials(content)
            print(f"成功加载 {len(self.question_answer_pairs)} 道题目")
            
        except FileNotFoundError:
            print(f"错误：找不到材料文件 {self.materials_file}")
        except Exception as e:
            print(f"加载材料时出错：{e}")
    
    def _parse_materials(self, content: str):
        """解析材料文件内容"""
        lines = content.split('\n')

        # 找到题目部分和答案部分的标志行
        questions_start = None
        answers_start = None

        for i, line in enumerate(lines):
            if "问题" in line and "*" in line:
                questions_start = i + 1  # 跳过标志行，从下一行开始
            elif "答案" in line and "*" in line:
                answers_start = i + 1  # 跳过标志行，从下一行开始
                break

        if questions_start is None or answers_start is None:
            print("警告：无法找到题目或答案部分的标志")
            return

        print(f"找到题目部分开始位置：第{questions_start+1}行")
        print(f"找到答案部分开始位置：第{answers_start+1}行")

        # 提取题目部分（从questions_start到answers_start-2，排除答案标志行）
        question_lines = lines[questions_start:answers_start-2]
        # 提取答案部分（从answers_start到文件末尾，但排除问卷星部分）
        answer_lines = lines[answers_start:]

        # 解析题目
        current_questions = []
        for line_num, line in enumerate(question_lines, start=questions_start+1):
            line = line.strip()
            if line and not line.startswith('*') and not line.startswith('#'):
                # 提取编号和题目内容，使用". "作为分隔符
                if '. ' in line:
                    parts = line.split('. ', 1)
                    if len(parts) == 2:
                        try:
                            num = int(parts[0])
                            question = parts[1].strip()
                            if question:  # 只要有内容就保留
                                current_questions.append((num, question))
                                if len(current_questions) <= 5:  # 显示前5个作为调试信息
                                    print(f"解析题目 {num}: {question[:30]}...")
                        except ValueError:
                            continue

        # 解析答案
        current_answers = []
        for line_num, line in enumerate(answer_lines, start=answers_start+1):
            line = line.strip()
            # 遇到问卷星部分就停止
            if "问卷星" in line or "***********************" in line:
                break
            if line and not line.startswith('*') and not line.startswith('#'):
                if '. ' in line:
                    parts = line.split('. ', 1)
                    if len(parts) == 2:
                        try:
                            num = int(parts[0])
                            answer = parts[1].strip()
                            if answer:
                                current_answers.append((num, answer))
                                if len(current_answers) <= 5:  # 显示前5个作为调试信息
                                    print(f"解析答案 {num}: {answer[:30]}...")
                        except ValueError:
                            continue

        print(f"成功解析 {len(current_questions)} 个问题")
        print(f"成功解析 {len(current_answers)} 个答案")

        # 匹配题目和答案
        answer_dict = {num: answer for num, answer in current_answers}

        matched_count = 0
        for num, question in current_questions:
            if num in answer_dict:
                self.question_answer_pairs.append({
                    'id': num,
                    'question': question,
                    'answer': answer_dict[num]
                })
                matched_count += 1

        print(f"成功匹配 {matched_count} 对问题-答案")

        # 验证总数是否为340
        if len(self.question_answer_pairs) != 340:
            print(f"警告：期望340道题目，实际获得{len(self.question_answer_pairs)}道题目")
    
    def get_random_questions(self, num_questions: int = 3) -> List[Dict]:
        """
        随机选择指定数量的题目
        
        Args:
            num_questions: 要选择的题目数量
            
        Returns:
            选中的题目列表
        """
        if len(self.question_answer_pairs) < num_questions:
            print(f"警告：可用题目数量({len(self.question_answer_pairs)})少于请求数量({num_questions})")
            return self.question_answer_pairs.copy()
        
        selected = random.sample(self.question_answer_pairs, num_questions)
        return selected
    
    def save_selected_questions(self, questions: List[Dict], filename: str = "selected_questions.json"):
        """保存选中的题目到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(questions, f, ensure_ascii=False, indent=2)
            print(f"已保存选中的题目到 {filename}")
        except Exception as e:
            print(f"保存题目时出错：{e}")
    
    def load_selected_questions(self, filename: str = "selected_questions.json") -> List[Dict]:
        """从文件加载选中的题目"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                questions = json.load(f)
            print(f"已从 {filename} 加载 {len(questions)} 道题目")
            return questions
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return []
        except Exception as e:
            print(f"加载题目时出错：{e}")
            return []

def test_materials():
    """测试材料加载功能"""
    print("测试实验材料加载...")
    
    # 创建材料管理器
    materials = ExperimentMaterials()
    
    if materials.question_answer_pairs:
        print(f"\n总共加载了 {len(materials.question_answer_pairs)} 道题目")
        
        # 显示前3道题目作为示例
        print("\n前3道题目示例：")
        for i, qa in enumerate(materials.question_answer_pairs[:3]):
            print(f"{i+1}. 题目：{qa['question']}")
            print(f"   答案：{qa['answer'][:50]}...")
            print()
        
        # 随机选择3道题目
        selected = materials.get_random_questions(3)
        print("随机选择的3道题目：")
        for i, qa in enumerate(selected):
            print(f"{i+1}. {qa['question']}")
        
        # 保存选中的题目
        materials.save_selected_questions(selected)
    else:
        print("未能加载任何题目")

if __name__ == "__main__":
    test_materials()
