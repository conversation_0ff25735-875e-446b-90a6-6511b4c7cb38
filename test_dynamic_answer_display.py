#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试动态答案显示和漂移校正功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from experiment_display import ExperimentDisplay
from eyelink_manager import EyeLinkManager

def test_dynamic_answer_display():
    """测试动态答案显示功能"""
    print("="*50)
    print("测试动态答案显示功能")
    print("="*50)

    # 创建显示管理器（虚拟模式用于测试）
    display = ExperimentDisplay(fullscreen=False)
    print(f"显示模式: {'虚拟模式' if display.dummy_mode else 'PsychoPy模式'}")

    # 创建EyeLink管理器（虚拟模式）
    eyelink = EyeLinkManager("test_dyn", "test_data", (1920, 1080), dummy_mode=True)
    eyelink.connect()
    
    try:
        print("\n1. 测试动态答案显示...")
        test_answer = """这是一个测试答案，用来验证动态时间控制功能。
        当被试注视屏幕下方的"读完看我哦"按钮区域持续1秒后，
        程序会自动切换到下一个屏幕。这个功能可以确保被试
        真正读完了答案内容，而不是简单地等待固定时间。
        
        在实际实验中，EyeLink会实时监测被试的眼动位置，
        当检测到持续注视按钮区域时，会自动继续实验流程。"""
        
        print("显示动态答案（模拟模式下会自动结束）...")
        start_time = time.time()
        
        result = display.show_answer(
            answer=test_answer,
            eyelink_manager=eyelink,
            gaze_duration_threshold=1.0
        )
        
        duration = time.time() - start_time
        print(f"答案显示完成，用时: {duration:.2f}秒")
        print(f"显示结果: {'成功' if result else '被中断'}")
        
        print("\n2. 测试十字准星和漂移校正...")
        print("显示十字准星并执行漂移校正（模拟模式）...")
        
        start_time = time.time()
        result = display.show_fixation_cross(
            duration=3.0,
            eyelink_manager=eyelink,
            perform_drift_correction=True
        )
        
        duration = time.time() - start_time
        print(f"十字准星显示完成，用时: {duration:.2f}秒")
        print(f"显示结果: {'成功' if result else '被中断'}")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        eyelink.close()
        display.close()
    
    print("\n测试完成！")

def test_gaze_position():
    """测试眼动位置获取功能"""
    print("="*50)
    print("测试眼动位置获取功能")
    print("="*50)
    
    # 创建EyeLink管理器（虚拟模式）
    eyelink = EyeLinkManager("test_gaze", "test_data", (1920, 1080), dummy_mode=True)
    
    if eyelink.connect():
        print("✓ EyeLink连接成功（虚拟模式）")
        
        # 测试眼动位置获取
        print("\n测试眼动位置获取...")
        for i in range(5):
            gaze_pos = eyelink.get_current_gaze_position()
            if gaze_pos:
                print(f"第{i+1}次: 眼动位置 = {gaze_pos}")
            else:
                print(f"第{i+1}次: 无法获取眼动位置（虚拟模式正常）")
            time.sleep(0.5)
        
        eyelink.close()
        print("✓ EyeLink连接已关闭")
    else:
        print("✗ EyeLink连接失败")

def main():
    """主函数"""
    print("动态答案显示和漂移校正功能测试")
    print("="*60)
    
    try:
        # 测试1：动态答案显示
        test_dynamic_answer_display()
        
        print("\n" + "="*60)
        
        # 测试2：眼动位置获取
        test_gaze_position()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n所有测试完成！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
