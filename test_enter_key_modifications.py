#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试回车键修改功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from experiment_display import ExperimentDisplay
from eyelink_manager import EyeLinkManager

def test_question_display():
    """测试问题显示（按回车继续）"""
    print("="*50)
    print("测试问题显示功能")
    print("="*50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        print("测试问题显示（按回车继续）...")
        test_question = "这是一个测试问题，用来验证按回车键继续的功能。在实际实验中，被试需要按回车键才能进入下一个阶段。"
        
        if display.dummy_mode:
            print("虚拟模式：问题显示测试")
            print(f"问题：{test_question}")
            result = display.show_question(test_question)
            print(f"显示结果: {'成功' if result else '被中断'}")
        else:
            print("PsychoPy模式：请在窗口中按回车键继续")
            result = display.show_question(test_question)
            print(f"显示结果: {'成功' if result else '被中断'}")
        
    except Exception as e:
        print(f"问题显示测试失败: {e}")
    
    finally:
        display.close()

def test_answer_display():
    """测试答案显示（按回车继续）"""
    print("\n" + "="*50)
    print("测试答案显示功能")
    print("="*50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        print("测试答案显示（按回车继续）...")
        test_answer = """这是一个测试答案，用来验证按回车键继续的功能。
        
现在的修改：
• 暂时注释掉了眼动检测功能
• 改为按回车键继续
• 仍然显示"读完看我哦"按钮作为视觉提示
• 支持Esc键跳过

在实际实验中，被试需要按回车键才能进入下一个阶段。"""
        
        if display.dummy_mode:
            print("虚拟模式：答案显示测试")
            print(f"答案：{test_answer[:50]}...")
            result = display.show_answer(test_answer)
            print(f"显示结果: {'成功' if result else '被中断'}")
        else:
            print("PsychoPy模式：请在窗口中按回车键继续")
            result = display.show_answer(test_answer)
            print(f"显示结果: {'成功' if result else '被中断'}")
        
    except Exception as e:
        print(f"答案显示测试失败: {e}")
    
    finally:
        display.close()

def test_rating():
    """测试评分功能（按回车确认）"""
    print("\n" + "="*50)
    print("测试评分功能")
    print("="*50)
    
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        print("测试评分功能（不限时，按回车确认）...")
        
        if display.dummy_mode:
            print("虚拟模式：评分测试")
            rating = display.get_rating(
                "您觉得这个测试如何？",
                (1, 5),
                ["很差", "较差", "一般", "较好", "很好"]
            )
            print(f"评分结果: {rating}")
        else:
            print("PsychoPy模式：请选择数字键，然后按回车确认")
            rating = display.get_rating(
                "您觉得这个测试如何？",
                (1, 5),
                ["很差", "较差", "一般", "较好", "很好"]
            )
            print(f"评分结果: {rating}")
        
    except Exception as e:
        print(f"评分测试失败: {e}")
    
    finally:
        display.close()

def test_fixation_cross():
    """测试十字准星显示（合并的基线校准）"""
    print("\n" + "="*50)
    print("测试十字准星显示功能")
    print("="*50)
    
    display = ExperimentDisplay(fullscreen=False)
    eyelink = EyeLinkManager("test", "test_data", (1920, 1080), dummy_mode=True)
    eyelink.connect()
    
    try:
        print("测试十字准星显示（基线校准+漂移校正）...")
        
        if display.dummy_mode:
            print("虚拟模式：十字准星测试")
            result = display.show_fixation_cross(
                duration=3.0,
                eyelink_manager=eyelink,
                perform_drift_correction=True
            )
            print(f"显示结果: {'成功' if result else '被中断'}")
        else:
            print("PsychoPy模式：观察十字准星和提示文字颜色")
            result = display.show_fixation_cross(
                duration=3.0,
                eyelink_manager=eyelink,
                perform_drift_correction=True
            )
            print(f"显示结果: {'成功' if result else '被中断'}")
        
    except Exception as e:
        print(f"十字准星测试失败: {e}")
    
    finally:
        eyelink.close()
        display.close()

def test_experiment_flow():
    """测试实验流程的修改"""
    print("\n" + "="*50)
    print("测试实验流程修改")
    print("="*50)
    
    try:
        from experiment_flow import CuriosityExperiment
        
        # 创建实验对象
        experiment = CuriosityExperiment("test", use_eyelink=False, fullscreen=False)
        
        print("实验时间参数配置：")
        for key, value in experiment.timing.items():
            if value is None:
                print(f"  {key}: 动态控制（按键继续）")
            else:
                print(f"  {key}: {value}")
        
        print(f"\n✓ 实验流程修改验证完成")
        
    except Exception as e:
        print(f"✗ 实验流程测试失败: {e}")

def main():
    """主函数"""
    print("回车键修改功能测试")
    print("="*80)
    print("本程序测试以下修改：")
    print("1. 问题显示：按回车键继续")
    print("2. 答案显示：按回车键继续（暂时注释眼动检测）")
    print("3. 评分功能：不限时，按回车确认")
    print("4. 十字准星：合并基线校准和漂移校正，白色提示文字")
    print("="*80)
    
    try:
        # 测试1：问题显示
        test_question_display()
        
        # 测试2：答案显示
        test_answer_display()
        
        # 测试3：评分功能
        test_rating()
        
        # 测试4：十字准星
        test_fixation_cross()
        
        # 测试5：实验流程
        test_experiment_flow()
        
        print("\n" + "="*80)
        print("🎉 所有修改测试完成！")
        print("="*80)
        print("修改总结：")
        print("✓ 问题显示改为按回车继续")
        print("✓ 答案显示改为按回车继续（眼动检测已注释）")
        print("✓ 评分功能改为不限时，按回车确认")
        print("✓ 十字准星合并基线校准和漂移校正")
        print("✓ 提示文字使用白色，不刺激")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
