#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
好奇心瞳孔冷知识实验 - 主程序
整合所有模块，提供完整的实验界面和控制
"""

import os
import sys
import time
import traceback
from datetime import datetime
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入实验模块
try:
    from experiment_flow import CuriosityExperiment
    from data_manager import DataManager
    from experiment_materials import ExperimentMaterials
    from eyelink_manager import EyeLinkManager
    from experiment_display import ExperimentDisplay
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

class ExperimentLauncher:
    """实验启动器"""
    
    def __init__(self):
        """初始化实验启动器"""
        self.experiment = None
        self.data_manager = None
        
    def check_dependencies(self) -> bool:
        """检查依赖库"""
        print("检查实验依赖...")
        
        if not MODULES_AVAILABLE:
            print("✗ 实验模块导入失败")
            return False
        
        # 检查基本Python库
        required_modules = [
            ('json', 'JSON处理'),
            ('time', '时间控制'),
            ('random', '随机数生成'),
            ('os', '操作系统接口'),
            ('datetime', '日期时间')
        ]
        
        missing_modules = []
        for module_name, description in required_modules:
            try:
                __import__(module_name)
                print(f"✓ {description}: {module_name}")
            except ImportError:
                print(f"✗ {description}: {module_name}")
                missing_modules.append(module_name)
        
        # 检查可选依赖
        optional_modules = [
            ('psychopy', 'PsychoPy实验平台'),
            ('pylink', 'EyeLink眼动仪接口'),
            ('pandas', '数据分析'),
            ('numpy', '数值计算')
        ]
        
        for module_name, description in optional_modules:
            try:
                __import__(module_name)
                print(f"✓ {description}: {module_name}")
            except ImportError:
                print(f"⚠ {description}: {module_name} (可选，将使用模拟模式)")
        
        if missing_modules:
            print(f"\n缺少必需模块: {', '.join(missing_modules)}")
            return False
        
        print("\n✓ 依赖检查完成")
        return True
    
    def get_participant_info(self) -> Optional[dict]:
        """获取被试信息"""
        print("\n" + "="*50)
        print("好奇心瞳孔冷知识实验")
        print("="*50)
        
        try:
            # 被试ID
            participant_id = input("请输入被试ID: ").strip()
            if not participant_id:
                participant_id = f"test_{datetime.now().strftime('%m%d_%H%M')}"
                print(f"使用默认ID: {participant_id}")
            
            # 实验设置
            print("\n实验设置:")
            use_eyelink_input = input("是否使用EyeLink眼动仪? (y/n, 默认y): ").strip().lower()
            use_eyelink = use_eyelink_input != 'n'  # 默认为True，只有输入n才为False

            fullscreen_input = input("是否全屏显示? (y/n, 默认y): ").strip().lower()
            fullscreen = fullscreen_input != 'n'  # 默认为True，只有输入n才为False

            # 题目数量
            try:
                num_questions = int(input("题目数量 (默认1): ").strip() or "1")
                if num_questions < 1 or num_questions > 10:
                    print("题目数量设置为默认值: 1")
                    num_questions = 1
            except ValueError:
                print("输入无效，使用默认值: 1")
                num_questions = 1
            
            return {
                'participant_id': participant_id,
                'use_eyelink': use_eyelink,
                'fullscreen': fullscreen,
                'num_questions': num_questions
            }
            
        except KeyboardInterrupt:
            print("\n用户取消输入")
            return None
        except Exception as e:
            print(f"获取被试信息时出错: {e}")
            return None
    
    def run_experiment(self, participant_info: dict) -> bool:
        """运行实验"""
        try:
            print(f"\n准备为被试 {participant_info['participant_id']} 启动实验...")
            
            # 创建实验对象
            self.experiment = CuriosityExperiment(
                participant_id=participant_info['participant_id'],
                use_eyelink=participant_info['use_eyelink'],
                fullscreen=participant_info['fullscreen']
            )
            
            # 数据管理器已在实验对象中创建
            self.data_manager = self.experiment.data_manager
            self.data_manager.log_event("EXPERIMENT_START",
                                      f"Participant: {participant_info['participant_id']}")
            
            # 显示实验信息
            print(f"\n实验信息:")
            print(f"被试ID: {participant_info['participant_id']}")
            print(f"题目数量: {participant_info['num_questions']}")
            print(f"使用EyeLink: {'是' if participant_info['use_eyelink'] else '否'}")
            print(f"全屏显示: {'是' if participant_info['fullscreen'] else '否'}")
            print(f"数据保存目录: {self.experiment.data_dir}")
            
            # 确认开始
            input("\n按回车键开始实验...")
            
            # 运行实验
            success = self.experiment.run_experiment(participant_info['num_questions'])
            
            if success:
                self.data_manager.log_event("EXPERIMENT_COMPLETE", "Experiment completed successfully")
                print("\n✓ 实验成功完成！")
                
                # 显示数据摘要
                self._show_data_summary()
                
                return True
            else:
                self.data_manager.log_event("EXPERIMENT_FAILED", "Experiment failed to complete")
                print("\n✗ 实验未能完成")
                return False
                
        except KeyboardInterrupt:
            print("\n实验被用户中断")
            if self.data_manager:
                self.data_manager.log_event("EXPERIMENT_INTERRUPTED", "User interrupted")
            return False
        except Exception as e:
            print(f"\n实验运行时出错: {e}")
            print("错误详情:")
            traceback.print_exc()
            if self.data_manager:
                self.data_manager.log_event("EXPERIMENT_ERROR", str(e))
            return False
    
    def _show_data_summary(self):
        """显示数据摘要"""
        if not self.data_manager:
            return
        
        print("\n" + "="*50)
        print("实验数据摘要")
        print("="*50)
        
        summary = self.data_manager.get_data_summary()
        
        print(f"数据目录: {summary['data_dir']}")
        print(f"总试次数: {summary['total_trials']}")
        print(f"完成率: {summary['completion_rate']:.1f}%")
        
        if summary['average_ratings']:
            print("\n平均评分:")
            for rating_type, avg_score in summary['average_ratings'].items():
                print(f"  {rating_type}: {avg_score:.2f}")
        
        print(f"\n生成的文件:")
        for filename in summary['files_created']:
            print(f"  - {filename}")
        
        # 数据验证
        validation = self.data_manager.validate_data()
        print(f"\n数据验证: {'通过' if validation['is_valid'] else '失败'}")
        
        if validation['issues']:
            print("发现问题:")
            for issue in validation['issues']:
                print(f"  ✗ {issue}")
        
        if validation['warnings']:
            print("警告:")
            for warning in validation['warnings']:
                print(f"  ⚠ {warning}")
    
    def cleanup(self):
        """清理资源"""
        if self.experiment:
            self.experiment.cleanup()
        print("资源清理完成")

def main():
    """主函数"""
    launcher = ExperimentLauncher()
    
    try:
        # 检查依赖
        if not launcher.check_dependencies():
            print("\n依赖检查失败，无法启动实验")
            return
        
        # 获取被试信息
        participant_info = launcher.get_participant_info()
        if not participant_info:
            print("未能获取被试信息，退出程序")
            return
        
        # 运行实验
        success = launcher.run_experiment(participant_info)
        
        if success:
            print("\n实验程序正常结束")
        else:
            print("\n实验程序异常结束")
    
    except Exception as e:
        print(f"\n程序运行时出现未处理的错误: {e}")
        traceback.print_exc()
    
    finally:
        # 清理资源
        launcher.cleanup()
        
        # 等待用户确认
        input("\n按回车键退出程序...")

def test_mode():
    """测试模式 - 快速验证所有模块"""
    print("="*50)
    print("测试模式 - 验证实验模块")
    print("="*50)
    
    # 测试材料加载
    print("\n1. 测试材料加载...")
    try:
        materials = ExperimentMaterials()
        if materials.question_answer_pairs:
            print(f"✓ 成功加载 {len(materials.question_answer_pairs)} 道题目")
            
            # 显示示例题目
            sample = materials.get_random_questions(1)[0]
            print(f"示例题目: {sample['question'][:50]}...")
        else:
            print("✗ 未能加载题目")
    except Exception as e:
        print(f"✗ 材料加载测试失败: {e}")
    
    # 测试EyeLink管理器
    print("\n2. 测试EyeLink管理器...")
    try:
        eyelink = EyeLinkManager("test", dummy_mode=True)
        if eyelink.connect():
            print("✓ EyeLink管理器测试通过")
        else:
            print("✗ EyeLink管理器测试失败")
    except Exception as e:
        print(f"✗ EyeLink测试失败: {e}")
    
    # 测试显示管理器
    print("\n3. 测试显示管理器...")
    try:
        display = ExperimentDisplay(fullscreen=False)
        print("✓ 显示管理器创建成功")
        display.close()
    except Exception as e:
        print(f"✗ 显示管理器测试失败: {e}")
    
    # 测试数据管理器
    print("\n4. 测试数据管理器...")
    try:
        data_manager = DataManager("test_data")
        data_manager.log_event("TEST", "测试事件")
        print("✓ 数据管理器测试通过")
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {e}")
    
    print("\n测试模式完成")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_mode()
    else:
        main()
